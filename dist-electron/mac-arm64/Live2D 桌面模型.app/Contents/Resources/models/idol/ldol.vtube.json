{"Version": 1, "Name": "ldol", "ModelID": "43e742fa07f94aa5927db22642794f30", "FileReferences": {"Icon": "111.png", "Model": "ldol.model3.json", "IdleAnimation": "", "IdleAnimationWhenTrackingLost": ""}, "ModelSaveMetadata": {"LastSavedVTubeStudioVersion": "1.28.15", "LastSavedPlatform": "Steam", "LastSavedDateUTC": "Friday, 21 June 2024, 14:06:35", "LastSavedDateLocalTime": "Friday, 21 June 2024, 22:06:35", "LastSavedDateUnixMillisecondTimestamp": "1718978795111"}, "SavedModelPosition": {"Position": {"x": 8.205402374267578, "y": -76.3383560180664, "z": 0.0}, "Rotation": {"x": 0.0, "y": 0.0, "z": 0.0, "w": 1.0}, "Scale": {"x": 1.6168938875198364, "y": 1.6168938875198364, "z": 1.0}}, "ModelPositionMovement": {"Use": false, "X": 5, "Y": 3, "Z": 2, "SmoothingX": 10, "SmoothingY": 10, "SmoothingZ": 10}, "ItemSettings": {"OnlyMoveWhenPinned": false, "AllowNormalHotkeyTriggers": true, "Multiplier_HeadAngleX": 1.0, "Multiplier_HeadAngleY": 1.0, "Multiplier_HeadAngleZ": 1.0, "Shift_HeadAngleX": 0.0, "Shift_HeadAngleY": 0.0, "Smoothing_HeadAngleX": 15.0, "Smoothing_HeadAngleY": 15.0, "Smoothing_HeadAngleZ": 15.0}, "PhysicsSettings": {"Use": true, "UseLegacyPhysics": false, "Live2DPhysicsFPS": 3, "PhysicsStrength": 45, "WindStrength": 0, "DraggingPhysicsStrength": 0}, "GeneralSettings": {"TimeUntilTrackingLostIdleAnimation": 0.0, "WorkshopSharingForbidden": true, "EnableExpressionSaving": false}, "ParameterSettings": [{"Folder": "", "Name": "Face Left/Right Rotation", "Input": "FaceAngleX", "InputRangeLower": -30.0, "InputRangeUpper": 30.0, "OutputRangeLower": -30.0, "OutputRangeUpper": 30.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamAngleX", "Smoothing": 15, "Minimized": false}, {"Folder": "", "Name": "Face Up/Down Rotation", "Input": "FaceAngleY", "InputRangeLower": -20.0, "InputRangeUpper": 20.0, "OutputRangeLower": -30.0, "OutputRangeUpper": 30.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamAngleY", "Smoothing": 41, "Minimized": false}, {"Folder": "", "Name": "Face Lean Rotation", "Input": "FaceAngleZ", "InputRangeLower": -30.0, "InputRangeUpper": 30.0, "OutputRangeLower": -30.0, "OutputRangeUpper": 30.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamAngleZ", "Smoothing": 15, "Minimized": false}, {"Folder": "", "Name": "Body Rotation X", "Input": "FaceAngleX", "InputRangeLower": -30.0, "InputRangeUpper": 30.0, "OutputRangeLower": -10.0, "OutputRangeUpper": 10.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamBodyAngleX", "Smoothing": 29, "Minimized": false}, {"Folder": "", "Name": "Body Rotation Y", "Input": "FaceAngleY", "InputRangeLower": -40.0, "InputRangeUpper": 40.0, "OutputRangeLower": -10.0, "OutputRangeUpper": 10.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamBodyAngleY", "Smoothing": 51, "Minimized": false}, {"Folder": "", "Name": "Body Rotation Z", "Input": "FaceAngleZ", "InputRangeLower": -20.0, "InputRangeUpper": 20.0, "OutputRangeLower": -10.0, "OutputRangeUpper": 10.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamBodyAngleZ", "Smoothing": 11, "Minimized": false}, {"Folder": "", "Name": "Eye Open Left", "Input": "EyeOpenLeft", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": 0.0, "OutputRangeUpper": 1.899999976158142, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamEyeLOpen", "Smoothing": 10, "Minimized": false}, {"Folder": "", "Name": "Eye Smile Left", "Input": "MouthSmile", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": 0.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamEyeLSmile", "Smoothing": 10, "Minimized": false}, {"Folder": "", "Name": "Eye Open Right", "Input": "EyeOpenRight", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": 0.0, "OutputRangeUpper": 1.899999976158142, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamEyeROpen", "Smoothing": 10, "Minimized": false}, {"Folder": "", "Name": "Eye Smile Right", "Input": "MouthSmile", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": 0.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamEyeRSmile", "Smoothing": 10, "Minimized": false}, {"Folder": "", "Name": "Eye X", "Input": "EyeRightX", "InputRangeLower": -1.0, "InputRangeUpper": 1.0, "OutputRangeLower": 1.0, "OutputRangeUpper": -1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamEyeBallX", "Smoothing": 8, "Minimized": false}, {"Folder": "", "Name": "Eye Y", "Input": "EyeRightY", "InputRangeLower": -1.0, "InputRangeUpper": 1.0, "OutputRangeLower": -1.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamEyeBallY", "Smoothing": 8, "Minimized": false}, {"Folder": "", "Name": "Brow Height Left", "Input": "Brows", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": -1.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamBrowLY", "Smoothing": 10, "Minimized": false}, {"Folder": "", "Name": "Brow Form Left", "Input": "Brows", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": -1.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamBrowLForm", "Smoothing": 15, "Minimized": false}, {"Folder": "", "Name": "Mouth Smile", "Input": "MouthSmile", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": -1.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamMouthForm", "Smoothing": 0, "Minimized": false}, {"Folder": "", "Name": "Mouth Open", "Input": "MouthOpen", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": 0.0, "OutputRangeUpper": 2.0999999046325684, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "ParamMouthOpenY", "Smoothing": 0, "Minimized": false}, {"Folder": "", "Name": "Auto Breath", "Input": "", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": 0.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": true, "OutputLive2D": "ParamBreath", "Smoothing": 0, "Minimized": false}, {"Folder": "", "Name": "", "Input": "MouthX", "InputRangeLower": -1.0, "InputRangeUpper": 1.0, "OutputRangeLower": -30.0, "OutputRangeUpper": 30.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "Param20", "Smoothing": 20, "Minimized": false}, {"Folder": "", "Name": "", "Input": "<PERSON><PERSON><PERSON><PERSON>", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": 0.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "Param21", "Smoothing": 20, "Minimized": false}, {"Folder": "", "Name": "", "Input": "MouthShrug", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": 0.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "Param48", "Smoothing": 20, "Minimized": false}, {"Folder": "", "Name": "", "Input": "MouthFunnel", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": 0.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "Param45", "Smoothing": 20, "Minimized": false}, {"Folder": "", "Name": "", "Input": "JawOpen", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": 0.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "Param50", "Smoothing": 20, "Minimized": false}, {"Folder": "", "Name": "", "Input": "<PERSON><PERSON>ucker", "InputRangeLower": -1.0, "InputRangeUpper": 1.0, "OutputRangeLower": -1.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "Param49", "Smoothing": 20, "Minimized": false}, {"Folder": "", "Name": "", "Input": "FaceAngry", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": 0.0, "OutputRangeUpper": 30.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "Param53", "Smoothing": 20, "Minimized": false}, {"Folder": "", "Name": "", "Input": "TongueOut", "InputRangeLower": 0.0, "InputRangeUpper": 1.0, "OutputRangeLower": 0.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "Param46", "Smoothing": 20, "Minimized": false}, {"Folder": "", "Name": "", "Input": "MouthPressLipOpen", "InputRangeLower": -1.2999999523162842, "InputRangeUpper": 1.2999999523162842, "OutputRangeLower": -1.0, "OutputRangeUpper": 1.0, "ClampInput": false, "ClampOutput": false, "UseBlinking": false, "UseBreathing": false, "OutputLive2D": "Param47", "Smoothing": 20, "Minimized": false}], "Hotkeys": [{"HotkeyID": "4999066cc2f24f7cadff0863aa2fa1d2", "Name": "1", "Action": "ToggleExpression", "File": "1.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "Triggers": {"Trigger1": "N1", "Trigger2": "", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": false, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.5, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "173a122e16cf4d2093b05dcd8dbab89b", "Name": "2", "Action": "ToggleExpression", "File": "2.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "Triggers": {"Trigger1": "N2", "Trigger2": "", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": false, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.5, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "ef084882389f44e88eca043b3c016c82", "Name": "3", "Action": "ToggleExpression", "File": "3.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "Triggers": {"Trigger1": "N3", "Trigger2": "", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": false, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.5, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "f5709f6be6994770b6f6a3d50ab922e9", "Name": "4", "Action": "ToggleExpression", "File": "4.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "Triggers": {"Trigger1": "N4", "Trigger2": "", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": false, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.5, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "5fbadb5a48d9430c884e8ec8dda6e057", "Name": "5", "Action": "ToggleExpression", "File": "5.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "Triggers": {"Trigger1": "N5", "Trigger2": "", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": false, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.5, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "fda37a72e3e34512b471bd6e5605b1b3", "Name": "6", "Action": "ToggleExpression", "File": "6.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "Triggers": {"Trigger1": "N6", "Trigger2": "", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": false, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.5, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "9a9497e0eb0f4dfebae36c80a32ca8c5", "Name": "7", "Action": "ToggleExpression", "File": "7.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "Triggers": {"Trigger1": "N7", "Trigger2": "", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": false, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.5, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "15c14078f66b40109a78f5f2f46bb5aa", "Name": "8", "Action": "ToggleExpression", "File": "8.exp3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "Triggers": {"Trigger1": "N8", "Trigger2": "", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": false, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.5, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}, {"HotkeyID": "cd13865d4b4548ac8aad43824831b524", "Name": "", "Action": "TriggerAnimation", "File": "1.motion3.json", "Folder": "", "Position": {"X": 0.0, "Y": 0.0, "Z": 0.0, "Rotation": 0.0}, "ColorOverlay": {"On": false, "IsStaticColor": false, "Display": -1, "WindowName": "", "IncludeLeft": false, "IncludeMid": false, "IncludeRight": false, "BaseValue": 0, "OverlayValue": 0, "Smoothing": 0, "IncludeItems": false, "StaticColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}, "ColorScreenMultiplyPreset": {"ArtMeshMultiplyAndScreenColors": []}, "HandGestureSettings": {"GestureLeft": "", "GestureRight": "", "GestureCombinator": "AND", "AllowMirroredGesture": false, "DeactivateExpWhenGestureNotDetected": false, "SecondsUntilDetection": 0.5, "SecondsDetected": 0.0, "PercentDetected": 0.0}, "LoadModelSettings": {"LoadAtCurrentPosition": true}, "Triggers": {"Trigger1": "Q", "Trigger2": "", "Trigger3": "", "ScreenButton": -1}, "IsGlobal": true, "IsActive": true, "Minimized": false, "StopsOnLastFrame": false, "DeactivateAfterKeyUp": false, "OnlyLoadOneRandomItem": false, "DeactivateAfterSeconds": false, "DeactivateAfterSecondsAmount": 10.0, "FadeSecondsAmount": 0.5, "OnScreenHotkeyColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "HotkeySettings": {"UseOnScreenHotkeys": false, "UseKeyboardHotkeys": true, "SendOnScreenHotkeysToPC": true, "OnScreenHotkeyAlpha": 75}, "ArtMeshDetails": {"ArtMeshesExcludedFromPinning": [], "ArtMeshesThatDeleteItemsOnDrop": [], "ArtMeshSceneLightingMultipliers": [], "ArtMeshMultiplyAndScreenColors": []}, "ParameterCustomization": {"ParametersExcludedFromVNetSmoothing": []}, "PhysicsCustomizationSettings": {"PhysicsMultipliersPerPhysicsGroup": [], "WindMultipliersPerPhysicsGroup": [], "DraggingPhysicsMultipliersPerPhysicsGroup": []}, "FolderInfo": {"HotkeyFolders": [], "ConfigItemFolders": []}, "SavedActiveExpressions": []}