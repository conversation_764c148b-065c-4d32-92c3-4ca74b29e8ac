{"Version": 3, "Meta": {"Duration": 4.833, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 37, "TotalSegmentCount": 224, "TotalPointCount": 601, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Model", "Id": "Opacity", "Segments": [0, 1, 1, 0.42, 1, 0.84, 1, 1.27, 1, 0, 4.83, 1]}, {"Target": "Model", "Id": "EyeBlink", "Segments": [0, 1, 1, 0.311, 1, 0.622, 1, 0.933, 1, 1, 0.978, 1, 1.022, 0.003, 1.067, 0.003, 1, 1.911, 0.001, 2.756, 0, 3.6, 0, 1, 3.711, 0, 3.822, 0.997, 3.933, 0.997, 0, 4.833, 0.997]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.333, 0, 0.4, 0, 0.467, 0, 1, 0.544, 0, 0.622, 0, 0.7, 0, 1, 0.778, 0, 0.856, -30, 0.933, -30, 1, 1.022, -30, 1.111, 30, 1.2, 30, 1, 1.322, 30, 1.444, -30, 1.567, -30, 1, 1.733, -30, 1.9, -21.371, 2.067, -14.414, 1, 2.211, -8.384, 2.356, -1.605, 2.5, -0.937, 1, 2.711, 0.038, 2.922, 0, 3.133, 0, 1, 3.4, 0, 3.667, 0, 3.933, 0, 0, 4.833, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.089, 0, 0.178, -9, 0.267, -9, 1, 0.333, -9, 0.4, 12.934, 0.467, 17.383, 1, 0.544, 22.842, 0.622, 25.06, 0.7, 26.008, 1, 1.156, 28.495, 1.611, 27, 2.067, 27, 1, 2.133, 27, 2.2, 30, 2.267, 30, 1, 2.344, 30, 2.422, -27.904, 2.5, -28.404, 1, 2.833, -29.378, 3.167, -29.602, 3.5, -29.602, 1, 3.644, -29.602, 3.789, 0, 3.933, 0, 0, 4.833, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.333, 0, 0.4, 0, 0.467, 0, 1, 0.544, 0, 0.622, 0, 0.7, 0, 1, 0.778, 0, 0.856, -4, 0.933, -4, 1, 1.022, -4, 1.111, 7.748, 1.2, 7.748, 1, 1.322, 7.748, 1.444, -5.167, 1.567, -6.83, 1, 1.733, -7.753, 1.9, -8.524, 2.067, -8.524, 1, 2.089, -9.402, 2.111, -11.282, 2.133, -12, 1, 2.256, -19.047, 2.378, 26.698, 2.5, 26.698, 1, 2.833, 27.861, 3.167, 28, 3.5, 28, 1, 3.722, 28, 3.944, -4, 4.167, -4, 1, 4.311, -4, 4.456, 0, 4.6, 0, 0, 4.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.333, 0, 0.4, 0, 0.467, 0, 1, 0.622, 0, 0.778, 0, 0.933, 0, 1, 0.978, 0, 1.022, 0, 1.067, 0, 1, 1.4, 0, 1.733, 0, 2.067, 0, 1, 2.211, 0, 2.356, 0, 2.5, 0, 1, 2.867, 0, 3.233, 0, 3.6, 0, 1, 3.644, 0, 3.689, 0, 3.733, 0, 1, 3.767, 0, 3.8, 0, 3.833, 0, 1, 3.867, 0, 3.9, 0, 3.933, 0, 0, 4.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.333, 0, 0.4, 0, 0.467, 0, 1, 0.622, 0, 0.778, 0, 0.933, 0, 1, 0.978, 0, 1.022, -1, 1.067, -1, 1, 1.4, -1, 1.733, -1, 2.067, -1, 1, 2.211, -1, 2.356, -1.001, 2.5, -0.996, 1, 2.867, -0.982, 3.233, -0.965, 3.6, -0.965, 1, 3.644, -0.965, 3.689, -1, 3.733, -1, 1, 3.767, -1, 3.8, -0.72, 3.833, -0.4, 1, 3.867, -0.08, 3.9, 0, 3.933, 0, 0, 4.833, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 1, 1, 0.089, 1, 0.178, 1, 0.267, 1, 1, 0.333, 1, 0.4, 1.002, 0.467, 1.002, 1, 0.544, 1.002, 0.622, 1.04, 0.7, 0.7, 1, 0.778, 0.36, 0.856, -1, 0.933, -1, 1, 1.311, -1, 1.689, -1, 2.067, -1, 1, 2.211, -1, 2.356, -0.988, 2.5, -0.986, 1, 2.867, -0.98, 3.233, -0.979, 3.6, -0.973, 1, 3.711, -0.972, 3.822, 1, 3.933, 1, 0, 4.833, 1]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 1, 1, 0.089, 1, 0.178, 1, 0.267, 1, 1, 0.333, 1, 0.4, 1.002, 0.467, 1.002, 1, 0.544, 1.002, 0.622, 1.04, 0.7, 0.7, 1, 0.778, 0.36, 0.856, -1, 0.933, -1, 1, 1.311, -1, 1.689, -1, 2.067, -1, 1, 2.211, -1, 2.356, -0.988, 2.5, -0.986, 1, 2.867, -0.98, 3.233, -0.979, 3.6, -0.973, 1, 3.711, -0.972, 3.822, 1, 3.933, 1, 0, 4.833, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0.1, 0.267, 0.1, 1, 0.333, 0.1, 0.4, 0.101, 0.467, 0.097, 1, 0.544, 0.093, 0.622, 0, 0.7, 0, 1, 1.156, 0, 1.611, 0, 2.067, 0, 1, 2.211, 0, 2.356, 0, 2.5, 0, 1, 2.711, 0, 2.922, 0, 3.133, 0, 1, 3.4, 0, 3.667, 0, 3.933, 0, 0, 4.833, 0]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, 0, 1, 0.089, 0, 0.178, 2.806, 0.267, 4, 1, 0.333, 4.895, 0.4, 5.285, 0.467, 5.597, 1, 0.544, 5.96, 0.622, 6, 0.7, 6, 1, 0.778, 6, 0.856, 4.44, 0.933, 3, 1, 1.022, 1.492, 1.111, 0.206, 1.2, -1, 1, 1.322, -3.459, 1.444, -6, 1.567, -6, 1, 1.733, -6, 1.9, -6, 2.067, -6, 1, 2.156, -6, 2.244, 3, 2.333, 3, 1, 2.478, 3, 2.622, -6, 2.767, -6, 1, 2.889, -6, 3.011, -6.079, 3.133, -5.92, 1, 3.4, -5.575, 3.667, 0, 3.933, 0, 1, 4.1, 0, 4.267, -2, 4.433, -2, 0, 4.833, -2]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 0, 1, 0.089, 0, 0.178, 2.823, 0.267, 4, 1, 0.333, 4.883, 0.4, 5.24, 0.467, 5.567, 1, 0.544, 5.948, 0.622, 6, 0.7, 6, 1, 0.778, 6, 0.856, 5.163, 0.933, 3, 1, 1.022, 0.528, 1.111, -1, 1.2, -1, 1, 1.322, -1, 1.444, 0, 1.567, 0, 1, 1.733, 0, 1.9, -1, 2.067, -1, 1, 2.156, -1, 2.244, 3, 2.333, 3, 1, 2.478, 3, 2.622, -10, 2.767, -10, 1, 2.889, -10, 3.011, -10, 3.133, -10, 1, 3.4, -10, 3.667, 0, 3.933, 0, 1, 4.1, 0, 4.267, -1, 4.433, -1, 0, 4.833, -1]}, {"Target": "Parameter", "Id": "ParamLeftLeg", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.333, 0, 0.4, 0, 0.467, 0, 1, 0.544, 0, 0.622, 0, 0.7, 0, 1, 1.156, 0, 1.611, 0, 2.067, 0, 1, 2.211, 0, 2.356, 0, 2.5, 0, 1, 2.711, 0, 2.922, 0, 3.133, 0, 1, 3.4, 0, 3.667, 0, 3.933, 0, 0, 4.833, 0]}, {"Target": "Parameter", "Id": "ParamRightLeg", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.333, 0, 0.4, 0, 0.467, 0, 1, 0.544, 0, 0.622, 0, 0.7, 0, 1, 1.156, 0, 1.611, 0, 2.067, 0, 1, 2.211, 0, 2.356, 0, 2.5, 0, 1, 2.711, 0, 2.922, 0, 3.133, 0, 1, 3.4, 0, 3.667, 0, 3.933, 0, 0, 4.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.333, 0, 0.4, 0, 0.467, 0, 1, 0.544, 0, 0.622, 0, 0.7, 0, 1, 0.778, 0, 0.856, -3, 0.933, -3, 1, 1.022, -3, 1.111, 3, 1.2, 3, 1, 1.322, 3, 1.444, -3, 1.567, -3, 1, 1.733, -3, 1.9, 0, 2.067, 0, 1, 2.211, 0, 2.356, 0, 2.5, 0, 1, 2.711, 0, 2.922, 0, 3.133, 0, 1, 3.4, 0, 3.667, 0, 3.933, 0, 0, 4.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.089, 0, 0.178, 6.266, 0.267, 6.266, 1, 0.333, 6.266, 0.4, 0, 0.467, 0, 1, 0.544, 0, 0.622, 0, 0.7, 0, 1, 0.856, 0, 1.011, -0.384, 1.167, 1.279, 1, 1.333, 2.814, 1.5, 12.92, 1.667, 17.01, 1, 1.8, 18.288, 1.933, 18, 2.067, 18, 1, 2.211, 2.046, 2.356, -22, 2.5, -22, 1, 2.711, -22, 2.922, -22.255, 3.133, -21.372, 1, 3.4, -20.257, 3.667, 0, 3.933, 0, 0, 4.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 0, 0.7, 0, 1, 0.778, 0, 0.856, -8, 0.933, -8, 1, 1.022, -8, 1.111, 8, 1.2, 8, 1, 1.322, 8, 1.444, -17.73, 1.567, -20.105, 1, 1.733, -23.344, 1.9, -23.3, 2.067, -23.3, 1, 2.211, -23.3, 2.356, 21.227, 2.5, 23.072, 1, 2.711, 25.768, 2.922, 25.699, 3.133, 25.699, 1, 3.4, 25.699, 3.667, 0, 3.933, 0, 0, 4.833, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.333, 0, 0.4, 0, 0.467, 0, 1, 0.544, 0, 0.622, 0, 0.7, 0, 1, 1.156, 0, 1.611, 0, 2.067, 0, 1, 2.211, 0, 2.356, 0, 2.5, 0, 1, 2.711, 0, 2.922, 0, 3.133, 0, 1, 3.4, 0, 3.667, 0, 3.933, 0, 0, 4.833, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.333, 0, 0.4, 0, 0.467, 0, 1, 0.544, 0, 0.622, 0, 0.7, 0, 1, 1.156, 0, 1.611, 0, 2.067, 0, 1, 2.211, 0, 2.356, 0, 2.5, 0, 1, 2.711, 0, 2.922, 0, 3.133, 0, 1, 3.4, 0, 3.667, 0, 3.933, 0, 0, 4.833, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.333, 0, 0.4, 0, 0.467, 0, 1, 0.544, 0, 0.622, 0, 0.7, 0, 1, 1.156, 0, 1.611, 0, 2.067, 0, 1, 2.211, 0, 2.356, 0, 2.5, 0, 1, 2.711, 0, 2.922, 0, 3.133, 0, 1, 3.4, 0, 3.667, 0, 3.933, 0, 0, 4.833, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0, 0.267, 0, 1, 0.333, 0, 0.4, 0, 0.467, 0, 1, 0.544, 0, 0.622, 0, 0.7, 0, 1, 1.156, 0, 1.611, 0, 2.067, 0, 1, 2.211, 0, 2.356, 0, 2.5, 0, 1, 2.711, 0, 2.922, 0, 3.133, 0, 1, 3.4, 0, 3.667, 0, 3.933, 0, 0, 4.833, 0]}, {"Target": "PartOpacity", "Id": "PartHead", "Segments": [0, 1, 2, 1.27, 1, 0, 4.83, 1]}, {"Target": "PartOpacity", "Id": "PartHairFront", "Segments": [0, 1, 2, 1.27, 1, 0, 4.83, 1]}, {"Target": "PartOpacity", "Id": "PartEyeL", "Segments": [0, 1, 2, 1.27, 1, 0, 4.83, 1]}, {"Target": "PartOpacity", "Id": "PartEyeBallL", "Segments": [0, 1, 2, 1.27, 1, 0, 4.83, 1]}, {"Target": "PartOpacity", "Id": "PartEyeWhiteL", "Segments": [0, 1, 2, 1.27, 1, 0, 4.83, 1]}, {"Target": "PartOpacity", "Id": "PartEyeR", "Segments": [0, 1, 2, 1.27, 1, 0, 4.83, 1]}, {"Target": "PartOpacity", "Id": "PartEyeBallR", "Segments": [0, 1, 2, 1.27, 1, 0, 4.83, 1]}, {"Target": "PartOpacity", "Id": "PartEyeWhiteR", "Segments": [0, 1, 2, 1.27, 1, 0, 4.83, 1]}, {"Target": "PartOpacity", "Id": "PartHairSide", "Segments": [0, 1, 2, 1.27, 1, 0, 4.83, 1]}, {"Target": "PartOpacity", "Id": "PartMouth", "Segments": [0, 1, 2, 1.27, 1, 0, 4.83, 1]}, {"Target": "PartOpacity", "Id": "PartOral", "Segments": [0, 1, 2, 1.27, 1, 0, 4.83, 1]}, {"Target": "PartOpacity", "Id": "PartFace", "Segments": [0, 1, 2, 1.27, 1, 0, 4.83, 1]}, {"Target": "PartOpacity", "Id": "PartBody", "Segments": [0, 1, 2, 1.27, 1, 0, 4.83, 1]}, {"Target": "PartOpacity", "Id": "PartArmL", "Segments": [0, 1, 2, 1.27, 1, 0, 4.83, 1]}, {"Target": "PartOpacity", "Id": "PartArmR", "Segments": [0, 1, 2, 1.27, 1, 0, 4.83, 1]}, {"Target": "PartOpacity", "Id": "PartHairBack", "Segments": [0, 1, 2, 1.27, 1, 0, 4.83, 1]}]}