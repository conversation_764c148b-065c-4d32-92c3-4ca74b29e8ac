{"Version": 3, "Meta": {"Duration": 4.8, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 37, "TotalSegmentCount": 260, "TotalPointCount": 781, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Model", "Id": "Opacity", "Segments": [0, 1, 0, 4.8, 1]}, {"Target": "Model", "Id": "EyeBlink", "Segments": [0, 1, 0, 4.8, 1]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.1, 0, 0.2, -3, 0.3, -3, 1, 0.4, -3, 0.5, -2, 0.6, 0, 1, 0.7, 2, 0.8, 3, 0.9, 3, 1, 1, 3, 1.1, 2, 1.2, 0, 1, 1.3, -2, 1.4, -3, 1.5, -3, 1, 1.6, -3, 1.7, -2, 1.8, 0, 1, 1.9, 2, 2, 3, 2.1, 3, 1, 2.2, 3, 2.3, 2, 2.4, 0, 1, 2.5, -2, 2.6, -3, 2.7, -3, 1, 2.8, -3, 2.9, -2, 3, 0, 1, 3.1, 2, 3.2, 3, 3.3, 3, 1, 3.4, 3, 3.5, 2, 3.6, 0, 1, 3.7, -2, 3.8, -3, 3.9, -3, 1, 4, -3, 4.1, -2, 4.2, 0, 1, 4.3, 2, 4.4, 3, 4.5, 3, 1, 4.6, 3, 4.7, 0, 4.8, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 7.804, 1, 0.067, 7.804, 0.133, 30, 0.2, 30, 1, 0.3, 30, 0.4, 0, 0.5, 0, 1, 0.6, 0, 0.7, 30, 0.8, 30, 1, 0.9, 30, 1, 0, 1.1, 0, 1, 1.2, 0, 1.3, 30, 1.4, 30, 1, 1.5, 30, 1.6, 0, 1.7, 0, 1, 1.8, 0, 1.9, 30, 2, 30, 1, 2.1, 30, 2.2, 0, 2.3, 0, 1, 2.4, 0, 2.5, 30, 2.6, 30, 1, 2.7, 30, 2.8, 0, 2.9, 0, 1, 3, 0, 3.1, 30, 3.2, 30, 1, 3.3, 30, 3.4, 0, 3.5, 0, 1, 3.6, 0, 3.7, 30, 3.8, 30, 1, 3.9, 30, 4, 0, 4.1, 0, 1, 4.2, 0, 4.3, 30, 4.4, 30, 1, 4.5, 30, 4.6, 0, 4.7, 0, 1, 4.733, 0, 4.767, 7.804, 4.8, 7.804]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, -27.935, 1, 0.022, -27.935, 0.044, -30, 0.067, -30, 1, 0.267, -30, 0.467, 30, 0.667, 30, 1, 0.867, 30, 1.067, -30, 1.267, -30, 1, 1.467, -30, 1.667, 30, 1.867, 30, 1, 2.067, 30, 2.267, -30, 2.467, -30, 1, 2.667, -30, 2.867, 30, 3.067, 30, 1, 3.267, 30, 3.467, -30, 3.667, -30, 1, 3.867, -30, 4.067, 30, 4.267, 30, 1, 4.444, 30, 4.622, -27.935, 4.8, -27.935]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.2, 0, 0.4, 0, 0.6, 0, 1, 0.8, 0, 1, 0, 1.2, 0, 1, 1.4, 0, 1.6, 0, 1.8, 0, 1, 2, 0, 2.2, 0, 2.4, 0, 1, 2.6, 0, 2.8, 0, 3, 0, 1, 3.2, 0, 3.4, 0, 3.6, 0, 1, 3.8, 0, 4, 0, 4.2, 0, 1, 4.4, 0, 4.6, 0, 4.8, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0.1, 1, 0.2, 0.1, 0.4, 0.1, 0.6, 0.1, 1, 0.8, 0.1, 1, 0.1, 1.2, 0.1, 1, 1.4, 0.1, 1.6, 0.1, 1.8, 0.1, 1, 2, 0.1, 2.2, 0.1, 2.4, 0.1, 1, 2.6, 0.1, 2.8, 0.1, 3, 0.1, 1, 3.2, 0.1, 3.4, 0.1, 3.6, 0.1, 1, 3.8, 0.1, 4, 0.1, 4.2, 0.1, 1, 4.4, 0.1, 4.6, 0.1, 4.8, 0.1]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 1, 1, 0.2, 1, 0.4, 1, 0.6, 1, 1, 0.8, 1, 1, 1, 1.2, 1, 1, 1.4, 1, 1.6, 1, 1.8, 1, 1, 2, 1, 2.2, 1, 2.4, 1, 1, 2.6, 1, 2.8, 1, 3, 1, 1, 3.2, 1, 3.4, 1, 3.6, 1, 1, 3.8, 1, 4, 1, 4.2, 1, 1, 4.4, 1, 4.6, 1, 4.8, 1]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 1, 1, 0.2, 1, 0.4, 1, 0.6, 1, 1, 0.8, 1, 1, 1, 1.2, 1, 1, 1.4, 1, 1.6, 1, 1.8, 1, 1, 2, 1, 2.2, 1, 2.4, 1, 1, 2.6, 1, 2.8, 1, 3, 1, 1, 3.2, 1, 3.4, 1, 3.6, 1, 1, 3.8, 1, 4, 1, 4.2, 1, 1, 4.4, 1, 4.6, 1, 4.8, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.2, 0, 0.4, 0, 0.6, 0, 1, 0.8, 0, 1, 0, 1.2, 0, 1, 1.4, 0, 1.6, 0, 1.8, 0, 1, 2, 0, 2.2, 0, 2.4, 0, 1, 2.6, 0, 2.8, 0, 3, 0, 1, 3.2, 0, 3.4, 0, 3.6, 0, 1, 3.8, 0, 4, 0, 4.2, 0, 1, 4.4, 0, 4.6, 0, 4.8, 0]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, -3, 1, 0.1, -5.043, 0.2, -7, 0.3, -7, 1, 0.4, -7, 0.5, -4.182, 0.6, -1, 1, 0.7, 2.182, 0.8, 3, 0.9, 3, 1, 1, 3, 1.1, -3, 1.2, -3, 1, 1.3, -3, 1.4, -7, 1.5, -7, 1, 1.6, -7, 1.7, -4.182, 1.8, -1, 1, 1.9, 2.182, 2, 3, 2.1, 3, 1, 2.2, 3, 2.3, -3, 2.4, -3, 1, 2.5, -3, 2.6, -7, 2.7, -7, 1, 2.8, -7, 2.9, -4.182, 3, -1, 1, 3.1, 2.182, 3.2, 3, 3.3, 3, 1, 3.4, 3, 3.5, -3, 3.6, -3, 1, 3.7, -3, 3.8, -7, 3.9, -7, 1, 4, -7, 4.1, -4.182, 4.2, -1, 1, 4.3, 2.182, 4.4, 3, 4.5, 3, 1, 4.6, 3, 4.7, -3, 4.8, -3]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, -1, 1, 0.1, 1.154, 0.2, 3, 0.3, 3, 1, 0.4, 3, 0.5, 0.182, 0.6, -3, 1, 0.7, -6.182, 0.8, -7, 0.9, -7, 1, 1, -7, 1.1, -1, 1.2, -1, 1, 1.3, -1, 1.4, 3, 1.5, 3, 1, 1.6, 3, 1.7, 0.182, 1.8, -3, 1, 1.9, -6.182, 2, -7, 2.1, -7, 1, 2.2, -7, 2.3, -1, 2.4, -1, 1, 2.5, -1, 2.6, 3, 2.7, 3, 1, 2.8, 3, 2.9, 0.182, 3, -3, 1, 3.1, -6.182, 3.2, -7, 3.3, -7, 1, 3.4, -7, 3.5, -1, 3.6, -1, 1, 3.7, -1, 3.8, 3, 3.9, 3, 1, 4, 3, 4.1, 0.182, 4.2, -3, 1, 4.3, -6.182, 4.4, -7, 4.5, -7, 1, 4.6, -7, 4.7, -1, 4.8, -1]}, {"Target": "Parameter", "Id": "ParamLeftLeg", "Segments": [0, 7, 1, 0.089, 9.389, 0.178, 10, 0.267, 10, 1, 0.378, 10, 0.489, -3, 0.6, -3, 1, 0.756, -3, 0.911, -3, 1.067, -3, 1, 1.111, -3, 1.156, 7, 1.2, 7, 1, 1.289, 7, 1.378, 10, 1.467, 10, 1, 1.578, 10, 1.689, -3, 1.8, -3, 1, 1.956, -3, 2.111, -3, 2.267, -3, 1, 2.311, -3, 2.356, 7, 2.4, 7, 1, 2.489, 7, 2.578, 10, 2.667, 10, 1, 2.778, 10, 2.889, -3, 3, -3, 1, 3.156, -3, 3.311, -3, 3.467, -3, 1, 3.511, -3, 3.556, 7, 3.6, 7, 1, 3.689, 7, 3.778, 10, 3.867, 10, 1, 3.978, 10, 4.089, -3, 4.2, -3, 1, 4.356, -3, 4.511, -3, 4.667, -3, 1, 4.711, -3, 4.756, 7, 4.8, 7]}, {"Target": "Parameter", "Id": "ParamRightLeg", "Segments": [0, -3, 1, 0.156, -3, 0.311, -3, 0.467, -3, 1, 0.511, -3, 0.556, 5.676, 0.6, 7, 1, 0.689, 9.649, 0.778, 10, 0.867, 10, 1, 0.978, 10, 1.089, -3, 1.2, -3, 1, 1.356, -3, 1.511, -3, 1.667, -3, 1, 1.711, -3, 1.756, 5.676, 1.8, 7, 1, 1.889, 9.649, 1.978, 10, 2.067, 10, 1, 2.178, 10, 2.289, -3, 2.4, -3, 1, 2.556, -3, 2.711, -3, 2.867, -3, 1, 2.911, -3, 2.956, 5.676, 3, 7, 1, 3.089, 9.649, 3.178, 10, 3.267, 10, 1, 3.378, 10, 3.489, -3, 3.6, -3, 1, 3.756, -3, 3.911, -3, 4.067, -3, 1, 4.111, -3, 4.156, 5.676, 4.2, 7, 1, 4.289, 9.649, 4.378, 10, 4.467, 10, 1, 4.578, 10, 4.689, -3, 4.8, -3]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 8, 1, 0.167, -0.086, 0.333, -10, 0.5, -10, 1, 0.533, -10, 0.567, -10.096, 0.6, -8, 1, 0.767, 2.481, 0.933, 10, 1.1, 10, 1, 1.133, 10, 1.167, 8, 1.2, 8, 1, 1.367, 8, 1.533, -10, 1.7, -10, 1, 1.733, -10, 1.767, -10.096, 1.8, -8, 1, 1.967, 2.481, 2.133, 10, 2.3, 10, 1, 2.333, 10, 2.367, 8, 2.4, 8, 1, 2.567, 8, 2.733, -10, 2.9, -10, 1, 2.933, -10, 2.967, -10.096, 3, -8, 1, 3.167, 2.481, 3.333, 10, 3.5, 10, 1, 3.533, 10, 3.567, 8, 3.6, 8, 1, 3.767, 8, 3.933, -10, 4.1, -10, 1, 4.133, -10, 4.167, -10.096, 4.2, -8, 1, 4.367, 2.481, 4.533, 10, 4.7, 10, 1, 4.733, 10, 4.767, 8, 4.8, 8]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, -2, 1, 0.044, -4.9, 0.089, -7.05, 0.133, -9.664, 1, 0.144, -10.435, 0.156, -10.664, 0.167, -10.664, 1, 0.211, -10.664, 0.256, 5.048, 0.3, 5.048, 1, 0.4, 5.048, 0.5, 2.972, 0.6, -2, 1, 0.644, -4.364, 0.689, -7.117, 0.733, -9.664, 1, 0.744, -10.427, 0.756, -10.664, 0.767, -10.664, 1, 0.811, -10.664, 0.856, 5.048, 0.9, 5.048, 1, 1, 5.048, 1.1, -2, 1.2, -2, 1, 1.244, -2, 1.289, -9.664, 1.333, -9.664, 1, 1.344, -9.664, 1.356, -10.664, 1.367, -10.664, 1, 1.411, -10.664, 1.456, 5.048, 1.5, 5.048, 1, 1.6, 5.048, 1.7, -2, 1.8, -2, 1, 1.844, -2, 1.889, -9.664, 1.933, -9.664, 1, 1.944, -9.664, 1.956, -10.664, 1.967, -10.664, 1, 2.011, -10.664, 2.056, 5.048, 2.1, 5.048, 1, 2.2, 5.048, 2.3, -2, 2.4, -2, 1, 2.444, -2, 2.489, -9.664, 2.533, -9.664, 1, 2.544, -9.664, 2.556, -10.664, 2.567, -10.664, 1, 2.611, -10.664, 2.656, 5.048, 2.7, 5.048, 1, 2.8, 5.048, 2.9, -2, 3, -2, 1, 3.044, -2, 3.089, -9.664, 3.133, -9.664, 1, 3.144, -9.664, 3.156, -10.664, 3.167, -10.664, 1, 3.211, -10.664, 3.256, 5.048, 3.3, 5.048, 1, 3.4, 5.048, 3.5, -2, 3.6, -2, 1, 3.644, -2, 3.689, -9.664, 3.733, -9.664, 1, 3.744, -9.664, 3.756, -10.664, 3.767, -10.664, 1, 3.811, -10.664, 3.856, 5.048, 3.9, 5.048, 1, 4, 5.048, 4.1, -2, 4.2, -2, 1, 4.244, -2, 4.289, -9.664, 4.333, -9.664, 1, 4.344, -9.664, 4.356, -10.664, 4.367, -10.664, 1, 4.411, -10.664, 4.456, 5.048, 4.5, 5.048, 1, 4.6, 5.048, 4.7, -2, 4.8, -2]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.1, -15.897, 0.2, -30, 0.3, -30, 1, 0.4, -30, 0.5, -20, 0.6, 0, 1, 0.7, 20, 0.8, 30, 0.9, 30, 1, 1, 30, 1.1, 0, 1.2, 0, 1, 1.3, 0, 1.4, -30, 1.5, -30, 1, 1.6, -30, 1.7, -20, 1.8, 0, 1, 1.9, 20, 2, 30, 2.1, 30, 1, 2.2, 30, 2.3, 0, 2.4, 0, 1, 2.5, 0, 2.6, -30, 2.7, -30, 1, 2.8, -30, 2.9, -20, 3, 0, 1, 3.1, 20, 3.2, 30, 3.3, 30, 1, 3.4, 30, 3.5, 0, 3.6, 0, 1, 3.7, 0, 3.8, -30, 3.9, -30, 1, 4, -30, 4.1, -20, 4.2, 0, 1, 4.3, 20, 4.4, 30, 4.5, 30, 1, 4.6, 30, 4.7, 0, 4.8, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.2, 0, 0.4, 0, 0.6, 0, 1, 0.8, 0, 1, 0, 1.2, 0, 1, 1.4, 0, 1.6, 0, 1.8, 0, 1, 2, 0, 2.2, 0, 2.4, 0, 1, 2.6, 0, 2.8, 0, 3, 0, 1, 3.2, 0, 3.4, 0, 3.6, 0, 1, 3.8, 0, 4, 0, 4.2, 0, 1, 4.4, 0, 4.6, 0, 4.8, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 0.2, 0, 0.4, 0, 0.6, 0, 1, 0.8, 0, 1, 0, 1.2, 0, 1, 1.4, 0, 1.6, 0, 1.8, 0, 1, 2, 0, 2.2, 0, 2.4, 0, 1, 2.6, 0, 2.8, 0, 3, 0, 1, 3.2, 0, 3.4, 0, 3.6, 0, 1, 3.8, 0, 4, 0, 4.2, 0, 1, 4.4, 0, 4.6, 0, 4.8, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 1, 0.2, 0, 0.4, 0, 0.6, 0, 1, 0.8, 0, 1, 0, 1.2, 0, 1, 1.4, 0, 1.6, 0, 1.8, 0, 1, 2, 0, 2.2, 0, 2.4, 0, 1, 2.6, 0, 2.8, 0, 3, 0, 1, 3.2, 0, 3.4, 0, 3.6, 0, 1, 3.8, 0, 4, 0, 4.2, 0, 1, 4.4, 0, 4.6, 0, 4.8, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 1, 0.2, 0, 0.4, 0, 0.6, 0, 1, 0.8, 0, 1, 0, 1.2, 0, 1, 1.4, 0, 1.6, 0, 1.8, 0, 1, 2, 0, 2.2, 0, 2.4, 0, 1, 2.6, 0, 2.8, 0, 3, 0, 1, 3.2, 0, 3.4, 0, 3.6, 0, 1, 3.8, 0, 4, 0, 4.2, 0, 1, 4.4, 0, 4.6, 0, 4.8, 0]}, {"Target": "PartOpacity", "Id": "PartHead", "Segments": [0, 1, 0, 4.8, 1]}, {"Target": "PartOpacity", "Id": "PartHairFront", "Segments": [0, 1, 0, 4.8, 1]}, {"Target": "PartOpacity", "Id": "PartEyeL", "Segments": [0, 1, 0, 4.8, 1]}, {"Target": "PartOpacity", "Id": "PartEyeBallL", "Segments": [0, 1, 0, 4.8, 1]}, {"Target": "PartOpacity", "Id": "PartEyeWhiteL", "Segments": [0, 1, 0, 4.8, 1]}, {"Target": "PartOpacity", "Id": "PartEyeR", "Segments": [0, 1, 0, 4.8, 1]}, {"Target": "PartOpacity", "Id": "PartEyeBallR", "Segments": [0, 1, 0, 4.8, 1]}, {"Target": "PartOpacity", "Id": "PartEyeWhiteR", "Segments": [0, 1, 0, 4.8, 1]}, {"Target": "PartOpacity", "Id": "PartHairSide", "Segments": [0, 1, 0, 4.8, 1]}, {"Target": "PartOpacity", "Id": "PartMouth", "Segments": [0, 1, 0, 4.8, 1]}, {"Target": "PartOpacity", "Id": "PartOral", "Segments": [0, 1, 0, 4.8, 1]}, {"Target": "PartOpacity", "Id": "PartFace", "Segments": [0, 1, 0, 4.8, 1]}, {"Target": "PartOpacity", "Id": "PartBody", "Segments": [0, 1, 0, 4.8, 1]}, {"Target": "PartOpacity", "Id": "PartArmL", "Segments": [0, 1, 0, 4.8, 1]}, {"Target": "PartOpacity", "Id": "PartArmR", "Segments": [0, 1, 0, 4.8, 1]}, {"Target": "PartOpacity", "Id": "PartHairBack", "Segments": [0, 1, 0, 4.8, 1]}]}